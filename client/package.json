{"name": "assistant-ui-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --check .", "prettier:fix": "prettier --write ."}, "prettier": {"plugins": ["prettier-plugin-tailwindcss"], "tailwindStylesheet": "app/globals.css"}, "dependencies": {"@ai-sdk/openai": "^2.0.24", "@assistant-ui/react": "^0.10.50", "@assistant-ui/react-ai-sdk": "^1.0.6", "@assistant-ui/react-markdown": "^0.10.9", "@langchain/community": "^0.3.55", "@langchain/core": "^0.3.75", "@langchain/google-genai": "^0.2.17", "@langchain/openai": "^0.6.11", "@langchain/qdrant": "^0.1.3", "@qdrant/js-client-rest": "^1.15.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "ai": "^5.0.33", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.2", "framer-motion": "^12.23.12", "langchain": "^0.3.33", "lucide-react": "^0.542.0", "motion": "^12.23.12", "multer": "^2.0.2", "next": "15.5.2", "openai": "^5.19.1", "pdf-parse": "^1.1.1", "puppeteer": "^24.19.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-shiki": "^0.7.4", "remark-gfm": "^4.0.1", "streamifier": "^0.1.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.8", "youtubei.js": "^15.0.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "typescript": "^5"}}
import { GoogleGenerativeAIEmbeddings } from "@langchain/google-genai";
import { QdrantVectorStore } from "@langchain/qdrant";
import { OpenAI } from 'openai';

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

const client = new OpenAI({
    apiKey: GEMINI_API_KEY,
    baseURL: "https://generativelanguage.googleapis.com/v1beta/openai/"
});

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // Get the last user message
    const userMessage = messages[messages.length - 1];
    const user_query = userMessage.content;

    // Check for required environment variables
    if (!process.env.GEMINI_API_KEY) {
      return new Response("GEMINI_API_KEY is not configured", { status: 500 });
    }

    if (!process.env.QDRANT_URL || !process.env.COLLECTION_NAME) {
      return new Response("Qdrant configuration is missing. Please set QDRANT_URL and COLLECTION_NAME environment variables.", { status: 500 });
    }

    let relevantDocs = [];
    let context = "";

    try {
      const embeddings = new GoogleGenerativeAIEmbeddings({
        apiKey: process.env.GEMINI_API_KEY,
        modelName: "embedding-001",
      });

      const vectorStore = await QdrantVectorStore.fromExistingCollection(
        embeddings, {
          url: process.env.QDRANT_URL,
          collectionName: process.env.COLLECTION_NAME
        },
      );

      const vectorSearch = vectorStore.asRetriever({
        k: 3 // k is the number of documents to return
      });

      relevantDocs = await vectorSearch.invoke(user_query);
      context = relevantDocs.map(doc => doc.pageContent).join('\n\n');
    } catch (vectorError) {
      console.warn("Vector search failed, proceeding without context:", vectorError);
      // Continue without context if vector search fails
      context = "No relevant documents found in the knowledge base.";
    }

    const SYSTEM_PROMPT = `
    You are a helpful assistant that answers questions based on the provided context.
    Use the following context to answer the user's question:

    Context:
    ${context}

    If the context doesn't contain relevant information to answer the question,
    say "I don't have enough information in the provided context to answer that question."
    `;

    const response = await client.chat.completions.create({
      model: "gemini-2.5-pro",
      messages: [
        {role: "system", content: SYSTEM_PROMPT},
        {role: "user", content: user_query},
      ],
    });

    return new Response(response.choices[0].message.content, {
      headers: { 'Content-Type': 'text/plain' }
    });

  } catch (error) {
    console.error("Error occurred while processing chat:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

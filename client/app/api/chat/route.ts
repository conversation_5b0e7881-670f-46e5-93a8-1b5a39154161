import { GoogleGenerativeAIEmbeddings } from "@langchain/google-genai";
import { QdrantVectorStore } from "@langchain/qdrant";
import { openai } from "@ai-sdk/openai";
import { streamText, convertToModelMessages } from "ai";

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // Validate messages array
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return new Response("Invalid messages format", { status: 400 });
    }

    // Get the last user message
    const userMessage = messages[messages.length - 1];
    const user_query = userMessage?.content;

    // Validate user query
    if (!user_query || typeof user_query !== 'string') {
      return new Response("Invalid user message", { status: 400 });
    }

    console.log("User query:", user_query);

    // Check for required environment variables
    if (!process.env.GEMINI_API_KEY) {
      return new Response("GEMINI_API_KEY is not configured", { status: 500 });
    }

    let relevantDocs = [];
    let context = "";

    try {
      const embeddings = new GoogleGenerativeAIEmbeddings({
        apiKey: process.env.GEMINI_API_KEY,
        modelName: "embedding-001",
      });

      const vectorStore = await QdrantVectorStore.fromExistingCollection(
        embeddings, {
          url: process.env.QDRANT_URL,
          collectionName: process.env.COLLECTION_NAME
        },
      );

      const vectorSearch = vectorStore.asRetriever({
        k: 3 // k is the number of documents to return
      });

      relevantDocs = await vectorSearch.invoke(user_query);
      context = relevantDocs.map(doc => doc.pageContent).join('\n\n');
    } catch (vectorError) {
      console.warn("Vector search failed, proceeding without context:", vectorError);
      // Continue without context if vector search fails
      context = "No relevant documents found in the knowledge base.";
    }

    const SYSTEM_PROMPT = `
    You are a helpful assistant that answers questions based on the provided context.
    Use the following context to answer the user's question:

    Context:
    ${context}

    If the context doesn't contain relevant information to answer the question,
    say "I don't have enough information in the provided context to answer that question."
    `;

    // Create messages array for the AI SDK
    const aiMessages = [
      { role: "system" as const, content: SYSTEM_PROMPT },
      { role: "user" as const, content: user_query }
    ];

    const result = streamText({
      model: openai("gpt-4o"),
      messages: aiMessages,
    });

    return result.toUIMessageStreamResponse();

  } catch (error) {
    console.error("Error occurred while processing chat:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

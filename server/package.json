{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "build": "npm install --legacy-peer-deps", "dev": "nodemon index.js", "vercel-build": "npm install --legacy-peer-deps", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=18.0.0"}, "dependencies": {"@langchain/core": "^0.3.72", "@langchain/google-genai": "^0.2.16", "@langchain/openai": "^0.6.9", "@langchain/qdrant": "^0.1.3", "@qdrant/js-client-rest": "^1.15.1", "cloudinary": "^2.7.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^5.1.0", "multer": "^2.0.2", "nodemon": "^3.1.10", "openai": "^5.15.0", "pdf-parse": "^1.1.1", "puppeteer": "^24.17.0", "streamifier": "^0.1.1", "youtubei.js": "^15.0.1"}}
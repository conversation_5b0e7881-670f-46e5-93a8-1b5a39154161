// app/layout.js
import { Inter } from "next/font/google";
import "./globals.css";
import { AppProvider } from './context/AppContext.js'; // Import the provider

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "NotebookLM",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AppProvider> {/* Wrap your application with the provider */}
          {children}
        </AppProvider>
      </body>
    </html>
  );
}